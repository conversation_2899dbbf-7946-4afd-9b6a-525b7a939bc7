/* order-pending.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.header-icon {
  margin-bottom: 20rpx;
}

.clock-icon {
  font-size: 60rpx;
  line-height: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 订单信息卡片 */
.order-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-details {
  margin-bottom: 30rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.order-number {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number-label {
  font-size: 26rpx;
  color: #666;
}

.order-number-value {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}

/* 金额信息卡片 */
.amount-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.amount-row.main {
  font-weight: bold;
}

.amount-label {
  font-size: 28rpx;
  color: #333;
}

.amount-note {
  font-size: 24rpx;
  color: #999;
}

.amount-value {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
}

.amount-divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 10rpx 0;
}

/* 温馨提示卡片 */
.tips-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  line-height: 1.6;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-left {
  flex: 1;
  margin-right: 20rpx;
}

.cancel-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
}

.cancel-btn:active {
  background: #e9ecef;
}

.action-right {
  display: flex;
  align-items: center;
}

.amount-info {
  margin-right: 20rpx;
  text-align: right;
}

.amount-info .amount-label {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}

.amount-info .amount-price {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: bold;
  line-height: 1;
  margin-top: 4rpx;
}

.pay-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
}

.pay-btn:active {
  opacity: 0.8;
}

.pay-btn.loading {
  background: #ccc;
  opacity: 0.6;
}

.pay-btn[disabled] {
  background: #ccc;
  opacity: 0.6;
}
